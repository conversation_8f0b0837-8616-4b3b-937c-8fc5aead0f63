import { searchProfessionalByIdParams } from "./types";
import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter";
import { IGetProfessionalInformationByIdRepository } from "@/domain/interfaces/repositories/searchProfessionals/ISearchProfessionalsByIdRepository";
import { ISearchProfessionalByIdUsecase } from "@/domain/interfaces/usecases/searchProfessinals/ISearchProfessionalByIdRepository";
import { IGetContactRepository } from "@/domain/interfaces/repositories/contact";
import { IGetProfessionalPublicationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalPublication/IGetProfessionalPublicationsByProfessionalIdRepository.ts";
import { IGetProfessionalExperiencesByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalExperience/IGetProfessionalExperiencesByProfessionalIdRepository.ts";
import { IGetProfessionalDiplomasByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalDiploma/IGetProfessionalDiplomasByProfessionalIdRepository.ts";
import { IGetLanguagesByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalLanguage/IGetLanguagesByProfessionalIdRepository.ts";
import { IGetUserByIdUsecase } from "@/domain/interfaces/usecases/user";
import { IGetPhotosByUserIdUsecase } from "@/domain/interfaces/usecases/photos";

export class SearchProfessionalByIdUsecase
  implements ISearchProfessionalByIdUsecase
{
  constructor(
    private readonly getProfessionalInformationRepository: IGetProfessionalInformationByIdRepository,
    private readonly professionalAvailabilitiesFilter: IProfessionalAvailabilitiesFilter,
    private readonly getContactRepository: IGetContactRepository,
    private readonly getProfessionalPublicationRepository: IGetProfessionalPublicationsByProfessionalIdRepository,
    private readonly getProfessionalExperiencesRepositoryy: IGetProfessionalExperiencesByProfessionalIdRepository,
    private readonly getProfessionalDiplomasRepository: IGetProfessionalDiplomasByProfessionalIdRepository,
    private readonly getProfessionalLanguagesRepository: IGetLanguagesByProfessionalIdRepository,
    private readonly getUserByIdUsecase: IGetUserByIdUsecase,
    private readonly getPhotosByUserIdUsecase: IGetPhotosByUserIdUsecase,
  ) {}

  async execute(searchParams: searchProfessionalByIdParams) {
    try {
      const data = await this.getProfessionalInformationRepository.execute({
        id: searchParams.id,
        today: searchParams.today,
      });

      if (!data) return null;

      const contacts = await this.getContactRepository.execute(
        data.utilisateur_id,
      );

      const publications =
        await this.getProfessionalPublicationRepository.execute(data.id);

      const experiences =
        await this.getProfessionalExperiencesRepositoryy.execute(data.id);

      const diplomas = await this.getProfessionalDiplomasRepository.execute(
        data.id,
      );

      const languages = await this.getProfessionalLanguagesRepository.execute(
        data.id,
      );

      const userReference = await this.getUserByIdUsecase.execute(
        data.utilisateur_id,
      );

      const photos = await this.getPhotosByUserIdUsecase.execute(
        data.utilisateur_id,
      );

      if (data.parametre_disponibilite.length === 0) {
        const out: ProfessionalCardDTO = {
          ...data,
          disponibilite: [],
          specialite: data.specialites_professionnel,
          contacts: contacts,
          publications: publications,
          experiences: experiences,
          diplomes: diplomas,
          langues: languages,
          email: userReference.email,
          photos: photos,
        };

        return out;
      }

      const formattedAvailalities: TimeSlotProffessionalCard[] =
        this.professionalAvailabilitiesFilter.filter(
          data.parametre_disponibilite[0],
          data.rendez_vous,
          data.evenement,
          data.parametre_disponibilite[0].temps_moyen_consulation,
        );

      const out: ProfessionalCardDTO = {
        ...data,
        disponibilite: formattedAvailalities,
        specialite: data.specialites_professionnel,
        horaire_hebdomadaire:
          data.parametre_disponibilite[0].horaire_hebdomadaire,
        contacts: contacts,
        publications: publications,
        experiences: experiences,
        diplomes: diplomas,
        langues: languages,
        types_consultation: data.types_consultation,
        email: userReference.email,
        photos: photos,
      };

      return out;
    } catch (error) {
      console.log("error", error);
    }
  }
}
