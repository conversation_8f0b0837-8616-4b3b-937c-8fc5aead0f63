import {
  Contact,
  DiplomeProfessionnel,
  EtablissementProfessionnel,
  Evenement,
  ExperienceProfessionnel,
  horaire_hebdomadaire,
  LangueParleeProfessionnel,
  MotClesProfessionnel,
  Professionnel,
  PublicationProfessionnel,
  RendezVous,
  SpecialiteProfessionnel,
} from "../models";
import {
  professionnels_types_consultation_enum,
  sexe_enum,
} from "../models/enums";
import { Photo } from "../models/Photo";
import { AvailabilitySettingsDTO } from "./AvailabililtySettingsDTO";

export type SearchProfessionalDTO = Professionnel & {
  rendez_vous: RendezVous[];
  specialites_professionnel: SpecialiteProfessionnel[];
  evenement: Evenement[];
  etablissements_professionnel: EtablissementProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO[];
  contacts: Contact[];
  mot_cles: MotClesProfessionnel[];
};

export type TimeSlotProffessionalCard = {
  date: string;
  start: string;
  end: string;
};

export type ProfessionalCardDTO = Professionnel & {
  specialite: SpecialiteProfessionnel[];
  disponibilite: TimeSlotProffessionalCard[];
  etablissements_professionnel: EtablissementProfessionnel[];
  horaire_hebdomadaire?: horaire_hebdomadaire[];
  contacts: Contact[];
  publications?: PublicationProfessionnel[];
  experiences?: ExperienceProfessionnel[];
  diplomes?: DiplomeProfessionnel[];
  langues?: LangueParleeProfessionnel[];
  types_consultation?: professionnels_types_consultation_enum;
  motCles?: MotClesProfessionnel[];
  email?: string;
  photos?: Photo[];
};

export type ProfessionalCompleteDTO = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
};

export type ProfessionalProfileData = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO;
  etablissements_professionnel: EtablissementProfessionnel[];
};
