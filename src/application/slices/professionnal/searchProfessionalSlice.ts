import { ProfessionalCardDTO } from "@/domain/DTOS/ProfessionalDTO";
import DateSplitter from "@/domain/services/DateSplitter";
import LoadEvents from "@/domain/services/LoadEvents";
import ProfessionalAvailabilitiesFilter from "@/domain/services/ProfessionalAvailabilitiesFilter";
import { SearchProfessionalByIdUsecase } from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalByIdUsecase";
import { SearchProfessionalsUsecase } from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalsUsecase";
import {
  searchProfessionalByIdParams,
  searchProfessionalsParams,
} from "@/domain/usecases/professional/GetProfessionnalInformations/types";
import { GetContactRepository } from "@/infrastructure/repositories/contact";
import { GetProfessionalDiplomasByProfessionalIdRepository } from "@/infrastructure/repositories/professionalDiploma/GetProfessionalDiplomasByProfessionalIdRepository.ts";
import { GetProfessionalExperiencesByProfessionalIdRepository } from "@/infrastructure/repositories/professionalExperience/GetProfessionalExperiencesByProfessionalIdRepository.ts";
import { GetLanguagesByProfessionalIdRepository } from "@/infrastructure/repositories/professionalLanguage/GetLanguagesByProfessionalIdRepository.ts";
import { GetProfessionalPublicationsByProfessionalIdRepository } from "@/infrastructure/repositories/professionalPublication/GetProfessionalPublicationsByProfessionalIdRepository.ts";
import SearchProfessionalByIdRepository from "@/infrastructure/repositories/searchProfessinals/SearchProfessionalByIdRepository";
import SearchProfessionalsRepository from "@/infrastructure/repositories/SearchProfessionalsRepository";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import dayjs from "dayjs";

interface SearchProfessionalSlice {
  professionals: ProfessionalCardDTO[];
  currentProfessional: ProfessionalCardDTO | null;
  loading: boolean;
  error: string | null;
  searchType: "symptome" | "name";
}

const initialState: SearchProfessionalSlice = {
  professionals: [],
  currentProfessional: null,
  loading: false,
  error: null,
  searchType: "symptome",
};

export const searchProfessional = createAsyncThunk(
  "searchProfessional/searchProfessionals",
  async (
    searchParams: Omit<searchProfessionalsParams, "today">,
    { rejectWithValue },
  ) => {
    try {
      const dateSplitter = new DateSplitter();
      const searchProfessionalsRepository = new SearchProfessionalsRepository();
      const loadEvent = new LoadEvents();
      const professionalAvailabilitiesFilter =
        new ProfessionalAvailabilitiesFilter(dateSplitter, loadEvent);
      const searchprofessionalsUsecase = new SearchProfessionalsUsecase(
        searchProfessionalsRepository,
        professionalAvailabilitiesFilter,
      );
      const today = dayjs().format("YYYY-MM-DD");

      const data = await searchprofessionalsUsecase.execute({
        ...searchParams,
        today: today,
      });

      return data;
    } catch (error) {
      return rejectWithValue(error.message || "Erreur");
    }
  },
);

export const searchProfessionalById = createAsyncThunk(
  "searchProfessional/searchProfessionalById",
  async (
    searchParams: Omit<searchProfessionalByIdParams, "today">,
    { rejectWithValue },
  ) => {
    try {
      const dateSplitter = new DateSplitter();
      const searchProfessionalByIdRepository =
        new SearchProfessionalByIdRepository();

      const loadEvent = new LoadEvents();

      const professionalAvailabilitiesFilter =
        new ProfessionalAvailabilitiesFilter(dateSplitter, loadEvent);

      const getContactRepository = new GetContactRepository();
      const getProfessionalPublicationRepository =
        new GetProfessionalPublicationsByProfessionalIdRepository();
      const getProfessionalExperiencesByProfessionalIdRepository =
        new GetProfessionalExperiencesByProfessionalIdRepository();
      const getProfessionalDiplomasByProfessionalIdRepository =
        new GetProfessionalDiplomasByProfessionalIdRepository();
      const getProfessionalLanguagesByProfessionalIdRepository =
        new GetLanguagesByProfessionalIdRepository();

      const searchprofessionalByIdUsecase = new SearchProfessionalByIdUsecase(
        searchProfessionalByIdRepository,
        professionalAvailabilitiesFilter,
        getContactRepository,
        getProfessionalPublicationRepository,
        getProfessionalExperiencesByProfessionalIdRepository,
        getProfessionalDiplomasByProfessionalIdRepository,
        getProfessionalLanguagesByProfessionalIdRepository,
      );
      const today = dayjs().format("YYYY-MM-DD");

      const out = await searchprofessionalByIdUsecase.execute({
        ...searchParams,
        today: today,
      });

      return out;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const searchProfessionalsSlice = createSlice({
  name: "searchProfessionals",
  initialState,
  reducers: {
    setSearchType(state, action: PayloadAction<"symptome" | "name">) {
      state.searchType = action.payload;
    },
    setIsLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },

    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    setCurrentProfessional: (
      state,
      action: PayloadAction<ProfessionalCardDTO | null>,
    ) => {
      state.currentProfessional = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(searchProfessional.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchProfessional.fulfilled, (state, action) => {
        state.professionals = action.payload;
        state.loading = false;
      })
      .addCase(searchProfessional.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(searchProfessionalById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchProfessionalById.fulfilled, (state, action) => {
        state.currentProfessional = action.payload;
        state.loading = false;
      })
      .addCase(searchProfessionalById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setError, setIsLoading, setCurrentProfessional, setSearchType } =
  searchProfessionalsSlice.actions;
export default searchProfessionalsSlice.reducer;
