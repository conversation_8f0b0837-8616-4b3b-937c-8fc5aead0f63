import { Plus, ChevronDown, X } from "lucide-react";
import { FieldError, UseFormRegister, Path } from "react-hook-form";
import { memo, ReactElement } from "react";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

// Type générique pour les éléments sélectionnables
export type SelectableItem = {
  id: string | number;
  [key: string]: any;
};

// Type personnalisé pour les erreurs de validation
export type ValidationError =
  | FieldError
  | {
      message?: string;
      type?: string;
    };

interface MultiSelectProps {
  // Liste complète des éléments disponibles
  itemsList: SelectableItem[];
  // Éléments actuellement sélectionnés
  selectedItems: SelectableItem[];
  // Fonction de callback pour gérer les changements de sélection
  handleItemsChange: (items: SelectableItem[]) => void;
  // Fonction register de react-hook-form
  register: UseFormRegister<CabinetMedicalFormDTO>;
  // Nom du champ dans le formulaire
  fieldName: Path<CabinetMedicalFormDTO>;
  // Propriété à afficher pour chaque élément
  displayProperty: string;
  // ID unique pour le dropdown
  dropdownId: string;
  // Texte à afficher quand aucun élément n'est sélectionné
  placeholderText: string;
  // Texte à afficher quand des éléments sont sélectionnés
  selectedText?: (count: number) => string;
  // Texte à afficher quand tous les éléments sont sélectionnés
  allSelectedText: string;
  // Titre pour la section des éléments sélectionnés
  selectedItemsTitle: string;
  // Erreur de validation
  error?: ValidationError;
  // Trier les éléments par ordre alphabétique
  sortAlphabetically?: boolean;
}

function MultiSelect({
  itemsList,
  selectedItems,
  handleItemsChange,
  register,
  fieldName,
  displayProperty,
  dropdownId,
  placeholderText,
  selectedText,
  allSelectedText,
  selectedItemsTitle,
  error,
  sortAlphabetically = true,
}: MultiSelectProps): ReactElement {
  // Filtrer les éléments non sélectionnés
  const availableItems = itemsList.filter(
    (item) => !selectedItems.some((selected) => selected.id === item.id)
  );

  // Trier les éléments si nécessaire
  const sortedAvailableItems = sortAlphabetically
    ? [...availableItems].sort((a, b) =>
        String(a[displayProperty as keyof typeof a] || "").localeCompare(
          String(b[displayProperty as keyof typeof b] || "")
        )
      )
    : availableItems;

  // Trier les éléments sélectionnés si nécessaire
  const sortedSelectedItems = sortAlphabetically
    ? [...selectedItems].sort((a, b) =>
        String(a[displayProperty as keyof typeof a] || "").localeCompare(
          String(b[displayProperty as keyof typeof b] || "")
        )
      )
    : selectedItems;

  // Gérer l'ouverture/fermeture du dropdown
  const toggleDropdown = () => {
    const dropdownList = document.getElementById(dropdownId);
    if (dropdownList?.classList.contains("hidden")) {
      dropdownList.classList.remove("hidden");
    } else {
      dropdownList?.classList.add("hidden");
    }
  };

  // Ajouter un élément à la sélection
  const addItem = (item: SelectableItem) => {
    handleItemsChange([...selectedItems, item]);
    // Fermer la liste après sélection
    document.getElementById(dropdownId)?.classList.add("hidden");
  };

  // Supprimer un élément de la sélection
  const removeItem = (itemId: string | number) => {
    handleItemsChange(selectedItems.filter((item) => item.id !== itemId));
  };

  return (
    <>
      {/* Champ de sélection multi-select personnalisé */}
      <div className="relative">
        {/* Champ de sélection stylisé comme un dropdown */}
        <div
          className={`w-full h-12 px-4 py-2 rounded-lg border ${
            error ? "border-red-500" : "border-gray-200"
          } text-gray-500 flex items-center justify-between cursor-pointer`}
          onClick={toggleDropdown}
        >
          <span>
            {selectedItems.length > 0
              ? selectedText
                ? selectedText(selectedItems.length)
                : `${selectedItems.length} élément(s) sélectionné(s)`
              : placeholderText}
          </span>
          <ChevronDown size={16} className="text-gray-400" />

          {/* Input caché pour react-hook-form */}
          <input
            type="hidden"
            id={`${fieldName}Input`}
            {...register(fieldName)}
          />
        </div>

        {/* Liste déroulante des options */}
        <div
          id={dropdownId}
          className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden"
        >
          {sortedAvailableItems.map((item) => (
            <div
              key={item.id}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
              onClick={() => addItem(item)}
            >
              <Plus size={16} className="text-meddoc-primary mr-2" />
              {item[displayProperty as keyof typeof item]}
            </div>
          ))}
          {availableItems.length === 0 && (
            <div className="px-4 py-2 text-gray-500 italic">
              {allSelectedText}
            </div>
          )}
        </div>

        {/* Affichage du message d'erreur */}
        {error && <p className="mt-1 text-sm text-red-500">{error.message}</p>}
      </div>

      {/* Affichage des éléments sélectionnés en dessous du champ */}
      {selectedItems.length > 0 && (
        <div className="mt-3">
          <p className="text-sm font-medium text-gray-700 mb-2">
            {selectedItemsTitle}
          </p>
          <div className="flex flex-wrap gap-2">
            {sortedSelectedItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center bg-meddoc-primary text-white px-3 py-1 rounded-full text-sm"
              >
                <span>{item[displayProperty as keyof typeof item]}</span>
                <button
                  type="button"
                  className="ml-2 focus:outline-none"
                  onClick={() => removeItem(item.id)}
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
}

export default memo(MultiSelect);
