import { ChangeEvent, memo } from "react";
import { LucideIcon, ChevronDown } from "lucide-react";
import { FieldError, UseFormRegister } from "react-hook-form";

interface FormFieldProps {
  id: string;
  label: string;
  placeholder: string;
  type?: "text" | "number" | "email" | "password" | "select";
  icon: LucideIcon;
  register: UseFormRegister<any>;
  required?: boolean;
  options?: { value: string; label: string }[];
  inputMode?: "text" | "numeric" | "tel" | "email" | "url";
  pattern?: string;
  className?: string;
  error?: FieldError;
  validation?: {
    required?: string;
    minLength?: { value: number; message: string };
    maxLength?: { value: number; message: string };
    pattern?: { value: RegExp; message: string };
    validate?: (value: any) => boolean | string;
  };
  value?: string;
  onChange?: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
}

const FormField = ({
  id,
  label,
  placeholder,
  type = "text",
  icon: Icon,
  register,
  required = false,
  options = [],
  inputMode,
  pattern,
  className = "col-span-1",
  error,
  validation,
  value,
  onChange,
}: FormFieldProps) => {
  // Préparer les règles de validation
  const registerOptions = {
    ...(validation || {}),
    ...(required && !validation?.required
      ? { required: `Le champ ${label.toLowerCase()} est requis` }
      : {}),
  };

  // Déterminer les classes CSS en fonction de l'état d'erreur
  const inputClasses = `w-full h-12 px-4 py-2 rounded-lg border ${
    error ? "border-red-500" : "border-gray-200"
  } text-gray-700 focus:outline-none focus:border-meddoc-primary`;

  // Gérer l'enregistrement avec react-hook-form et les événements onChange
  const registerProps = onChange
    ? { ...register(id, registerOptions), onChange, value }
    : register(id, registerOptions);

  return (
    <div className={className}>
      <div className="flex flex-col w-full">
        <label
          htmlFor={id}
          className="flex items-center text-sm text-gray-700 mb-2"
        >
          <Icon size={16} className="text-meddoc-primary mr-2" />
          {label}
          {required && "*"}
        </label>

        {type === "select" ? (
          <div className="relative">
            <select
              id={id}
              name={id}
              className={inputClasses + " text-gray-500 appearance-none"}
              aria-invalid={error ? "true" : "false"}
              aria-describedby={error ? `${id}-error` : undefined}
              {...registerProps}
            >
              {options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400" />
            </div>
          </div>
        ) : (
          <input
            type={type}
            id={id}
            name={id}
            placeholder={placeholder}
            className={inputClasses}
            inputMode={inputMode}
            pattern={pattern}
            aria-invalid={error ? "true" : "false"}
            aria-describedby={error ? `${id}-error` : undefined}
            {...registerProps}
          />
        )}

        {/* Affichage du message d'erreur */}
        {error && (
          <p id={`${id}-error`} className="mt-1 text-sm text-red-500">
            {error.message}
          </p>
        )}
      </div>
    </div>
  );
};

export default memo(FormField);
