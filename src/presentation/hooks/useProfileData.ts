import { ProfessionalCardDTO } from "@/domain/DTOS";
import DateSplitter from "@/domain/services/DateSplitter";
import LoadEvents from "@/domain/services/LoadEvents";
import ProfessionalAvailabilitiesFilter from "@/domain/services/ProfessionalAvailabilitiesFilter";
import GetPhotosByUserIdUsecase from "@/domain/usecases/photos/GetPhotosByUserIdUsecase";
import { SearchProfessionalByIdUsecase } from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalByIdUsecase";
import { GetUserByIdUsecase } from "@/domain/usecases/user";
import { GetContactRepository } from "@/infrastructure/repositories/contact";
import GetPhotosByUserIdRepository from "@/infrastructure/repositories/photos/GetPhotosByUserIdRepository";
import { GetProfessionalDiplomasByProfessionalIdRepository } from "@/infrastructure/repositories/professionalDiploma";
import { GetProfessionalExperiencesByProfessionalIdRepository } from "@/infrastructure/repositories/professionalExperience";
import { GetLanguagesByProfessionalIdRepository } from "@/infrastructure/repositories/professionalLanguage";
import { GetProfessionalPublicationsByProfessionalIdRepository } from "@/infrastructure/repositories/professionalPublication";
import SearchProfessionalByIdRepository from "@/infrastructure/repositories/searchProfessinals/SearchProfessionalByIdRepository";
import GetUserByIdRepository from "@/infrastructure/repositories/user/GetUserByIdRepository";
import { useEffect, useState } from "react";

const useProfileData = ({ id }: { id: number }) => {
  const [profileData, setProfileData] = useState<ProfessionalCardDTO | null>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>();

  useEffect(() => {
    const getProfileData = async () => {
      try {
        const searchProfessionalByIdRepository =
          new SearchProfessionalByIdRepository();
        const dateSpliter = new DateSplitter();
        const loadEvent = new LoadEvents();
        const professionalAvailabilitiesFilter =
          new ProfessionalAvailabilitiesFilter(dateSpliter, loadEvent);
        const getContactRepository = new GetContactRepository();
        const getProfessionalPublicationRepository =
          new GetProfessionalPublicationsByProfessionalIdRepository();
        const getProfessionalExperiencesRepository =
          new GetProfessionalExperiencesByProfessionalIdRepository();
        const getProfessionalDiplomasRepository =
          new GetProfessionalDiplomasByProfessionalIdRepository();
        const getProfessionalLanguagesRepository =
          new GetLanguagesByProfessionalIdRepository();
        const getUserByIdRepository = new GetUserByIdRepository();
        const getPhotosByUserIdRepository = new GetPhotosByUserIdRepository();

        const getUserByIdUsecase = new GetUserByIdUsecase(
          getUserByIdRepository,
        );
        const getPhotosByUserIdUsecase = new GetPhotosByUserIdUsecase(
          getPhotosByUserIdRepository,
        );

        const searchProfessionalByIdUsecase = new SearchProfessionalByIdUsecase(
          searchProfessionalByIdRepository,
          professionalAvailabilitiesFilter,
          getContactRepository,
          getProfessionalPublicationRepository,
          getProfessionalExperiencesRepository,
          getProfessionalDiplomasRepository,
          getProfessionalLanguagesRepository,
          getUserByIdUsecase,
          getPhotosByUserIdUsecase,
        );

        setLoading(true);

        const result = await searchProfessionalByIdUsecase.execute({
          id: id,
          today: new Date().toString(),
        });
        setProfileData(result);

        setLoading(false);
      } catch (error) {
        console.log(
          "Erreur lors de la recuperation des donnees du professionnel",
          error,
        );
        setError(error);

        if (loading) {
          setLoading(false);
        }
      }
    };

    getProfileData();
  }, []);

  return {
    profileData,
    loading,
    error,
    setLoading,
  };
};

export default useProfileData;
