import { PaymentMethod } from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { sexe_enum } from "@/domain/models/enums";

/**
 * Configuration du formulaire Cabinet Médical
 * Ce fichier centralise toutes les constantes et paramètres configurables
 * pour le formulaire d'inscription des cabinets médicaux
 */

// Configuration des uploads et images
export const UPLOAD_CONFIG = {
  MAX_CABINET_IMAGES: 4,
  ACCEPTED_IMAGE_TYPES: "image/*",
  MAX_IMAGE_SIZE_MB: 5, // Taille maximale en MB
};

// Configuration de la persistance des données
export const PERSISTENCE_CONFIG = {
  LOCAL_STORAGE_KEY: "cabinetMedicalFormData",
  AUTO_SAVE_DELAY_MS: 4000, // Délai avant sauvegarde automatique
  REDIRECT_DELAY_MS: 2000, // Délai avant redirection après soumission
};

// Configuration du formulaire
export const FORM_CONFIG = {
  VALIDATION_MODE: "onBlur" as const,
  REVALIDATION_MODE: "onChange" as const,

  // Champs requis pour le calcul de la progression
  REQUIRED_FIELDS: [
    "titre",
    "nom",
    "prenom",
    "sexe",
    "specialites",
    "numero_ordre",
    "nom_etablissement",
    "nom_responsable",
    "prenom_responsable",
    "equipe",
    "adresse",
    "region",
    "district",
    "commune",
    "fokotany",
    "email",
    "telephone",
    "presentation",
    "motDePasse",
    "confirmMotDePasse",
  ],

  // Tableaux qui doivent avoir au moins un élément
  REQUIRED_ARRAY_FIELDS: [
    "ordre_appartenance",
    "specialities",
    "typeConsultation",
    "paymentMethods",
  ],

  // Valeurs par défaut pour les champs du formulaire
  DEFAULT_VALUES: {
    profileImage: "",
    ordre_appartenance: [],
    specialities: [],
    motCles: [],
    diplomes: [],
    experiences: [],
    publications: [],
    langues: [],
    typeConsultation: [],
    paymentMethods: [],
    nouveauPatientAcceptes: false,
    sexe: sexe_enum.homme,
    // Valeurs par défaut pour l'établissement professionnel
    nom_etablissement: "",
    nom_responsable: "",
    prenom_responsable: "",
    equipe: "",
  },
};

// Options disponibles pour les méthodes de paiement
export const PAYMENT_METHODS: PaymentMethod[] = [
  { id: "1", name: "Espèces" },
  { id: "2", name: "Carte bancaire" },
  { id: "3", name: "Chèque" },
  { id: "4", name: "Virement" },
  { id: "5", name: "Mobile money" },
];

// Configuration de l'interface utilisateur
export const UI_CONFIG = {
  // Styles pour le conteneur principal
  BOX_STYLES: {
    mt: { xs: 1, sm: 2 },
    mb: { xs: 3, sm: 4 },
    px: { xs: 1, sm: 0 },
    width: "100%",
  },

  // Configuration des sections (panneaux)
  SECTIONS: {
    PANEL1: "panel1", // Informations professionnelles
    PANEL2: "panel2", // Localisation
    PANEL3: "panel3", // Contact
    PANEL4: "panel4", // Présentation
    PANEL5: "panel5", // Authentification
    PANEL6: "panel6", // Spécialités
    PANEL7: "panel7", // Services
  },

  // Hauteur des champs textarea
  TEXTAREA_ROWS: {
    PRESENTATION: 4,
  },
};
