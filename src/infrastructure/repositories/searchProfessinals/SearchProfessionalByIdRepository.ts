import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import { IGetProfessionalInformationByIdRepository } from "@/domain/interfaces/repositories/searchProfessionals/ISearchProfessionalsByIdRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
const PROFESSIONAL_TABLE_NAME = "professionnels";

class SearchProfessionalByIdRepository
  implements IGetProfessionalInformationByIdRepository
{
  async execute({ id, today }) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(
        `
          *,
          rendez_vous(*),
          specialites_professionnel(*),
          evenement(*),
          etablissements_professionnel(*),
          parametre_disponibilite(
            *,
            horaire_hebdomadaire(
              *,
              creneau_horaire(*)
            ),
            horaire_date_specifique(
              *,
              creneau_horaire(*)
            )
          )
      `,
      )
      .gte("rendez_vous.date_rendez_vous", today) // 📌 Vérifie si le rendez-vous est dans le futur
      .eq("id", id);

    if (error) throw error;

    return data[0] as SearchProfessionalDTO;
  }
}

export default SearchProfessionalByIdRepository;
